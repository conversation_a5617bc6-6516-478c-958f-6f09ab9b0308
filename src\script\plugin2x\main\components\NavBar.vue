<template>
    <nav class="nav-bar" :class="styleType === 1 ? 'nav-bar-1' : ''">
        <div class="logo">
            <img src="@/img/homePage/logo.png" alt="logo" class="logo-img" />
            <span class="logo-text">MCP服务清单管理平台</span>
        </div>
        <div class="menu">
            <ul class="menu-list" @mouseleave="handleMouseleave()">
                <li
                    v-for="item in menuList"
                    :key="item.value"
                    class="menu-item"
                    :class="{
                        active: isActive(item),
                        disabled: item.disabled,
                        hover: hoverIndex === item.value
                    }"
                    @click="item.children || handleClick(item)"
                    @mouseenter="handleMouseenter(item)"
                    @mouseleave="handleMouseleave(item)"
                >
                    <span class="menu-label">
                        {{ item.label }}
                        <i v-if="item.children" class="el-icon-arrow-down arrow-icon"></i>
                    </span>
                    <ul v-if="item.children" class="submenu" @mouseleave="handleMouseleave()">
                        <li
                            v-for="child in item.children"
                            :key="child.value"
                            class="submenu-item"
                            :class="{
                                active: activeIndex === child.value,
                                hover: hoverIndex === child.value,
                                disabled: child.disabled
                            }"
                            @click.stop="handleClick(child)"
                        >
                            {{ child.label }}
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </nav>
</template>
<script>
export default {
    name: 'NavBar',
    props: {
        styleType: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            activeIndex: 'home',
            hoverIndex: '',
            menuList: [
                { label: '首页', value: 'home' },
                {
                    label: '新增配置项',
                    value: 'config',
                    children: [
                        { label: 'MCP工具配置', value: 'configTool' },
                        { label: 'MCP资源配置', value: 'configResource' },
                        { label: 'MCP服务配置', value: 'configService' }
                    ]
                },
                {
                    label: '权限配置管理',
                    value: 'permission'
                },
                {
                    label: '大模型调用测试',
                    value: 'modelTest'
                }
            ]
        };
    },
    created() {
        const origin = window.location.href;
        const lastItem = origin.substring(origin.lastIndexOf('/') + 1);
        const isSub = lastItem.split('-');
        if (isSub.length === 1) {
            this.activeIndex = this.$route.name;
        } else {
            this.activeIndex = 'home';
            this.$emit('jumpRouter', 'home');
        }
    },
    methods: {
        isActive(item) {
            if (item.children && item.children.length) {
                return item.children.some((child) => child.value === this.activeIndex);
            }
            return this.activeIndex === item.value;
        },
        handleClick(item) {
            this.activeIndex = item.value;
            this.hoverIndex = '';
            this.$emit('jumpRouter', item.value);
        },
        handleMouseenter(item) {
            this.hoverIndex = item.value;
        },
        handleMouseleave(item = {}) {
            if (item.children) return;
            this.hoverIndex = '';
        }
    }
};
</script>
<style scoped lang="less">
.nav-bar {
    height: 4rem;
    display: flex;
    align-items: center;
    background: #1565ff;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.2);
    position: relative;
    .logo {
        position: absolute;
        left: 1rem;
        top: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        &-img {
            user-select: none;
            width: 2.25rem;
            height: 2.25rem;
        }
        &-text {
            margin-left: 0.75rem;
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 500;
            font-size: 1.375rem;
            color: #ffffff;
            line-height: 2rem;
        }
    }
    .menu {
        flex: 1;
        display: flex;
        justify-content: center;
        height: 100%;
        user-select: none;
        .menu-list {
            display: flex;
            align-items: center;
            gap: 1.25rem;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;
            .menu-item {
                position: relative;
                display: flex;
                align-items: center;
                padding: 0 1rem;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 600;
                font-size: 1rem;
                color: #ffffffd9;
                cursor: pointer;
                height: 2.25rem;
                .arrow-icon {
                    font-size: 1rem;
                    transition: transform 0.2s ease;
                }
                &.hover {
                    background-color: rgba(255, 255, 255, 0.15);
                    border-radius: 0.375rem;
                    .arrow-icon {
                        transform: rotate(180deg);
                    }
                }
                &.active {
                    color: #ffffff;
                    background-color: rgba(255, 255, 255, 0.15);
                    border-radius: 0.375rem;
                }
                &.disabled {
                    color: #ffffff88;
                    cursor: not-allowed;
                }
                .submenu {
                    display: none;
                    position: absolute;
                    left: 0;
                    top: 100%;
                    color: #333;
                    border-radius: 4px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    background: #ffffff;
                    list-style: none;
                    padding: 0.5rem 0;
                    margin-top: 0.5rem;
                    z-index: 10;
                    .submenu-item {
                        padding: 0.5rem 1rem;
                        white-space: nowrap;
                        cursor: pointer;
                        &.active {
                            background: #c9dfff;
                            font-weight: 600;
                        }
                        &:hover {
                            background: #c9dfff;
                        }
                        &.disabled {
                            color: #999;
                            cursor: not-allowed;
                        }
                    }
                }
                &.hover .submenu {
                    display: block;
                }
            }
        }
    }
}
.nav-bar-1 {
    background: #fff !important;
    .logo {
        display: none !important;
    }
    .menu-item {
        color: #409eff !important;
        &.hover {
            color: #ffffff !important;
            background-color: #409eff !important;
        }
        &.active {
            color: #ffffff !important;
            background-color: #409eff !important;
        }
    }
}
</style>
